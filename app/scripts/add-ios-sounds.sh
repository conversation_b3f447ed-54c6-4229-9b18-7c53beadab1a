#!/bin/bash

# <PERSON><PERSON>t to add sound files to iOS Xcode project
# This script helps add the charge_start.mp3 file to all iOS targets

echo "🎵 Adding sound files to iOS project..."

# Check if sound file exists
SOUND_FILE="src/assets/sounds/charge_start.mp3"
if [ ! -f "$SOUND_FILE" ]; then
    echo "❌ Sound file not found: $SOUND_FILE"
    echo "Please make sure the sound file exists in the assets directory"
    exit 1
fi

echo "✅ Sound file found: $SOUND_FILE"

# Copy sound files to iOS target directories
echo "📁 Copying sound files to iOS targets..."

cp "$SOUND_FILE" "ios/bppulse/charge_start.mp3"
cp "$SOUND_FILE" "ios/aralpulse/charge_start.mp3"
cp "$SOUND_FILE" "ios/bpUSpulse/charge_start.mp3"

echo "✅ Sound files copied to iOS target directories"

echo ""
echo "🔧 NEXT STEPS:"
echo "1. Open the iOS project in Xcode:"
echo "   cd ios && open bppulse.xcworkspace"
echo ""
echo "2. For each target (bppulse, aralpulse, bpUSpulse):"
echo "   - Right-click on the target folder in Xcode"
echo "   - Select 'Add Files to [target]'"
echo "   - Select the charge_start.mp3 file"
echo "   - Make sure 'Add to target' is checked"
echo "   - Click 'Add'"
echo ""
echo "3. After adding files, uncomment the iOS sound loading code in:"
echo "   src/hooks/useNotificationSound.ts (lines 99-131)"
echo ""
echo "4. Clean and rebuild the project:"
echo "   cd ios && xcodebuild clean"
echo "   cd .. && npm run ios"
echo ""
echo "🎉 Once completed, notification sounds will work on both iOS and Android!"
