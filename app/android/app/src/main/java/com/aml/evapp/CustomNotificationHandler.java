package com.aml.evapp;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.media.AudioAttributes;
import android.net.Uri;
import android.os.Build;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;

import com.urbanairship.AirshipReceiver;
import com.urbanairship.push.PushMessage;
import com.urbanairship.push.notifications.NotificationArguments;
import com.urbanairship.push.notifications.NotificationProvider;
import com.urbanairship.push.notifications.NotificationResult;

/**
 * Custom notification handler to ensure custom sounds work properly
 * This handler intercepts Airship notifications and applies custom sound configuration
 */
public class CustomNotificationHandler extends NotificationProvider {
    
    private static final String TAG = "CustomNotificationHandler";
    private static final String CUSTOM_SOUND_KEY = "sound";
    
    @Override
    protected NotificationResult onCreateNotification(@NonNull Context context, @NonNull NotificationArguments arguments) {
        Log.d(TAG, "Creating custom notification...");
        
        try {
            PushMessage message = arguments.getMessage();
            
            // Check if custom sound is specified in the push payload
            String customSound = null;
            if (message.getExtras() != null) {
                customSound = message.getExtras().getString(CUSTOM_SOUND_KEY);
                Log.d(TAG, "Custom sound from payload: " + customSound);
            }
            
            // If custom sound is specified, ensure the notification channel has it
            if (customSound != null && !customSound.isEmpty()) {
                ensureCustomSoundChannel(context, customSound);
            }
            
            // Let Airship handle the rest of the notification creation
            return super.onCreateNotification(context, arguments);
            
        } catch (Exception e) {
            Log.e(TAG, "Error in custom notification handler: " + e.getMessage(), e);
            // Fall back to default behavior
            return super.onCreateNotification(context, arguments);
        }
    }
    
    /**
     * Ensure notification channel exists with custom sound
     */
    private void ensureCustomSoundChannel(Context context, String soundName) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                NotificationManager notificationManager = 
                    (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
                
                String channelId = "bp_pulse_notifications_custom_" + soundName;
                String channelName = "BP Pulse Custom Sound Notifications";
                
                // Check if channel already exists
                NotificationChannel existingChannel = notificationManager.getNotificationChannel(channelId);
                if (existingChannel != null) {
                    Log.d(TAG, "Custom sound channel already exists: " + channelId);
                    return;
                }
                
                // Create new channel with custom sound
                NotificationChannel channel = new NotificationChannel(
                    channelId,
                    channelName,
                    NotificationManager.IMPORTANCE_HIGH
                );
                
                // Set custom sound
                Uri soundUri = Uri.parse("android.resource://" + context.getPackageName() + "/raw/" + soundName);
                AudioAttributes audioAttributes = new AudioAttributes.Builder()
                    .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                    .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                    .build();
                
                channel.setSound(soundUri, audioAttributes);
                channel.enableLights(true);
                channel.enableVibration(true);
                channel.setShowBadge(true);
                
                notificationManager.createNotificationChannel(channel);
                Log.d(TAG, "Created custom sound notification channel: " + channelId + " with sound: " + soundUri);
                
            } catch (Exception e) {
                Log.e(TAG, "Error creating custom sound channel: " + e.getMessage(), e);
            }
        }
    }
}
