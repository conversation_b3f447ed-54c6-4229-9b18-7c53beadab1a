package com.aml.evapp;

import android.app.Application;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.media.AudioAttributes;
import android.net.Uri;
import android.os.Build;
import android.util.Log;
import com.facebook.react.PackageList;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint;
import com.facebook.react.defaults.DefaultReactNativeHost;
import com.facebook.soloader.SoLoader;
import java.util.List;

// Reaniamted
import com.facebook.react.bridge.JSIModulePackage;

// AppsFlyer
import com.appsflyer.reactnative.RNAppsFlyerPackage;

// react-native-inappbrowser-reborn
import com.proyecto26.inappbrowser.RNInAppBrowserPackage;

public class MainApplication extends Application implements ReactApplication {

  private final ReactNativeHost mReactNativeHost =
      new DefaultReactNativeHost(this) {
        @Override
        public boolean getUseDeveloperSupport() {
          return BuildConfig.DEBUG;
        }

        @Override
        protected List<ReactPackage> getPackages() {
          @SuppressWarnings("UnnecessaryLocalVariable")
          List<ReactPackage> packages = new PackageList(this).getPackages();
          // Packages that cannot be autolinked yet can be added manually here, for example:
          // packages.add(new MyReactNativePackage());

          return packages;
        }

        @Override
        protected String getJSMainModuleName() {
          return "index";
        }

        @Override
        protected boolean isNewArchEnabled() {
          return BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
        }

        @Override
        protected Boolean isHermesEnabled() {
          return BuildConfig.IS_HERMES_ENABLED;
        }
      };

  @Override
  public ReactNativeHost getReactNativeHost() {
    return mReactNativeHost;
  }

  @Override
  public void onCreate() {
    super.onCreate();

    // Create notification channel with custom sound before any other initialization
    createNotificationChannel();

    SoLoader.init(this, /* native exopackage */ false);
    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      DefaultNewArchitectureEntryPoint.load();
    }
    ReactNativeFlipper.initializeFlipper(this, getReactNativeHost().getReactInstanceManager());
  }

  /**
   * Create notification channel with custom sound for Android 8.0+
   * This must be called before Airship initialization to ensure custom sound works
   */
  private void createNotificationChannel() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
      try {
        Log.d("MainApplication", "Creating notification channel with custom sound...");

        NotificationManager notificationManager = getSystemService(NotificationManager.class);
        String channelId = "bp_pulse_notifications";
        String channelName = "BP Pulse Notifications";
        String channelDescription = "Notifications for BP Pulse app with custom sound";

        // Check if channel already exists
        NotificationChannel existingChannel = notificationManager.getNotificationChannel(channelId);
        if (existingChannel != null) {
          Log.d("MainApplication", "Notification channel already exists, deleting and recreating...");
          notificationManager.deleteNotificationChannel(channelId);
        }

        // Create new channel with custom sound
        NotificationChannel channel = new NotificationChannel(
            channelId,
            channelName,
            NotificationManager.IMPORTANCE_HIGH
        );

        channel.setDescription(channelDescription);
        channel.enableLights(true);
        channel.enableVibration(true);
        channel.setShowBadge(true);

        // Set custom sound
        Uri soundUri = Uri.parse("android.resource://" + getPackageName() + "/" + R.raw.charge_start);
        AudioAttributes audioAttributes = new AudioAttributes.Builder()
            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
            .setUsage(AudioAttributes.USAGE_NOTIFICATION)
            .build();

        channel.setSound(soundUri, audioAttributes);

        // Create the channel
        notificationManager.createNotificationChannel(channel);

        Log.d("MainApplication", "Notification channel created successfully with custom sound: " + soundUri.toString());

      } catch (Exception e) {
        Log.e("MainApplication", "Error creating notification channel: " + e.getMessage(), e);
      }
    } else {
      Log.d("MainApplication", "Android version < 8.0, notification channel not needed");
    }
  }
}
