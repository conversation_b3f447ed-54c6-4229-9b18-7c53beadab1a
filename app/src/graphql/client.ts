import {
  Apollo<PERSON><PERSON>,
  ApolloLink,
  createHttpLink,
  InMemoryCache,
} from '@apollo/client';
import { onError } from '@apollo/client/link/error';
import {
  createAbortLink,
  createAuthLink,
  createRequestLink,
  createResponseLink,
  createTimerLink,
  createXrayLink,
} from '@bp/mfe-helper-apollo';
import { logger } from '@utils/logger';
import { getApplicationName, getVersion } from 'react-native-device-info';

export const createApolloClient = (
  apiURL: string,
  apiKey: string,
  getToken?: () => Promise<string | null> | string | null,
) => {
  const requestLink = createRequestLink({ logger });
  const responseLink = createResponseLink({ logger });
  const timerLink = createTimerLink({ logger });
  const abortLink = createAbortLink({ logger });
  const xrayLink = createXrayLink({ logger });
  const authLink = createAuthLink({ logger, getToken });

  // Enhanced error logging for network issues
  const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
    if (graphQLErrors) {
      graphQLErrors.forEach(({ message, locations, path }) => {
        logger.error(
          `GraphQL error: Message: ${message}, Location: ${locations}, Path: ${path}`
        );
      });
    }

    if (networkError) {
      logger.error('Apollo Network Error Details:', {
        message: networkError.message,
        name: networkError.name,
        stack: networkError.stack,
        operation: operation.operationName,
        variables: operation.variables,
        apiURL,
        apiKey: apiKey.substring(0, 10) + '...',
      });

      // Log specific network error types
      if (networkError.message === 'Network request failed') {
        logger.error('Network request failed - possible causes:');
        logger.error('1. No internet connection');
        logger.error('2. Server is down');
        logger.error('3. Firewall blocking request');
        logger.error('4. Invalid SSL certificate');
        logger.error('5. Request timeout');
      }
    }
  });

  /**
   * HTTP Link
   * Attaches the API key to the headers and points to the correct domain
   */
  const httpLink = createHttpLink({
    uri: apiURL,
    headers: {
      'x-api-key': apiKey,
      'x-app-name': getApplicationName(),
      'x-app-version': getVersion(),
    },
  });

  return new ApolloClient({
    link: ApolloLink.from([
      requestLink,
      errorLink,      // Add error link for enhanced logging
      abortLink,
      xrayLink,
      timerLink,
      responseLink,
      authLink,
      httpLink,
    ]),
    cache: new InMemoryCache(),
  });
};
