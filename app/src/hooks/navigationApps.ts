import { useConfig } from '@providers/ConfigProvider';
import { useEffect, useState } from 'react';
import { getApps, MapId } from 'react-native-map-link';

export function useNavigationApps(): MapId[] {
  const [availableApps, setAvailableApps] = useState<MapId[]>([]);
  const { map_mfe_config: mapConfig } = useConfig();

  useEffect(() => {
    (async () => {
      const result = await getApps({
        latitude: mapConfig.initialRegion.latitude,
        longitude: mapConfig.initialRegion.longitude,
        appsWhiteList: ['apple-maps', 'citymapper', 'google-maps', 'waze'],
      });
      setAvailableApps(result.map(app => app.id));
    })();
  }, [mapConfig.initialRegion.latitude, mapConfig.initialRegion.longitude]);

  return availableApps;
}
