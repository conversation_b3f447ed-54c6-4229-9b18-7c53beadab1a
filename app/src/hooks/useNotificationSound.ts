import { useEffect, useState, useCallback } from 'react';
import Sound from 'react-native-sound';
import { Platform } from 'react-native';

// Initialize sound category for playback
Sound.setCategory('Playback');

interface UseNotificationSoundOptions {
  soundFileName?: string;
  volume?: number;
  enabled?: boolean;
}

interface UseNotificationSoundReturn {
  playSound: () => void;
  isLoaded: boolean;
  isEnabled: boolean;
  setEnabled: (enabled: boolean) => void;
}

/**
 * Custom hook for playing notification sounds
 * @param options Configuration options for the sound
 * @returns Object with playSound function and loading state
 */
export const useNotificationSound = (
  options: UseNotificationSoundOptions = {}
): UseNotificationSoundReturn => {
  const {
    soundFileName = 'charge_start',
    volume = 0.8,
    enabled = true,
  } = options;

  const [sound, setSound] = useState<Sound | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isEnabled, setIsEnabled] = useState(enabled);

  // Play notification sound with error handling
  const playSound = useCallback(() => {
    if (sound && isLoaded && isEnabled) {
      sound.setVolume(volume);
      sound.play((success) => {
        if (!success) {
          console.warn('Failed to play notification sound');
        }
      });
    } else {
      // Graceful fallback - just log instead of playing sound
      if (!isLoaded) {
        console.log('Notification sound not loaded - skipping sound playback');
      } else if (!isEnabled) {
        console.log('Notification sound disabled - skipping sound playback');
      }
    }
  }, [sound, isLoaded, isEnabled, volume]);

  // Initialize sound on hook mount
  useEffect(() => {
    let notificationSound: Sound | null = null;

    const initializeSound = () => {
      // Platform-specific sound loading with fallback paths
      const tryLoadSound = (paths: string[], sourceType: any, index = 0): void => {
        if (index >= paths.length) {
          console.warn('Failed to load sound from all attempted paths - sound will be disabled');
          setIsLoaded(false);
          setIsEnabled(false); // Disable sound if it can't be loaded
          return;
        }

        const currentPath = paths[index];
        console.log(`Attempting to load sound from path: ${currentPath}`);

        notificationSound = new Sound(currentPath, sourceType, (error) => {
          if (error) {
            console.warn(`Failed to load sound from path: ${currentPath}`, error);
            // Try next path
            tryLoadSound(paths, sourceType, index + 1);
            return;
          }

          console.log('Notification sound loaded successfully');
          console.log('Sound path:', currentPath);
          console.log('Platform:', Platform.OS);
          setIsLoaded(true);
          setSound(notificationSound);
        });
      };

      if (Platform.OS === 'ios') {
        // For iOS, temporarily disable sound loading to prevent app crashes
        // TODO: Properly configure iOS sound files in Xcode project
        console.warn('iOS sound loading temporarily disabled - sound files need to be added to Xcode project');
        setIsLoaded(false);
        setIsEnabled(false);

        // Uncomment below when sound files are properly added to iOS bundle:
        /*
        try {
          // Try to use the sound file from assets (Metro bundler approach)
          const soundAsset = require('../../assets/sounds/charge_start.mp3');
          notificationSound = new Sound(soundAsset, '', (error) => {
            if (error) {
              console.warn('Failed to load sound using require(), trying bundle paths...', error);
              // Fallback to bundle paths
              const iosPaths = [
                `${soundFileName}.mp3`,           // Bundle root
                `sounds/${soundFileName}.mp3`,    // sounds subdirectory
                `Resources/sounds/${soundFileName}.mp3`, // Full path
              ];
              tryLoadSound(iosPaths, Sound.MAIN_BUNDLE);
              return;
            }

            console.log('Notification sound loaded successfully using require()');
            console.log('Platform:', Platform.OS);
            setIsLoaded(true);
            setSound(notificationSound);
          });
        } catch (requireError) {
          console.warn('Could not require sound file, trying bundle paths...', requireError);
          // Fallback to bundle paths
          const iosPaths = [
            `${soundFileName}.mp3`,           // Bundle root
            `sounds/${soundFileName}.mp3`,    // sounds subdirectory
            `Resources/sounds/${soundFileName}.mp3`, // Full path
          ];
          tryLoadSound(iosPaths, Sound.MAIN_BUNDLE);
        }
        */
      } else {
        // Android: try res/raw path
        const androidPaths = [
          soundFileName,                    // res/raw (no extension)
          `${soundFileName}.mp3`,          // with extension (fallback)
        ];
        tryLoadSound(androidPaths, Sound.MAIN_BUNDLE);
      }
    };

    initializeSound();

    return () => {
      if (notificationSound) {
        notificationSound.release();
        setIsLoaded(false);
        setSound(null);
      }
    };
  }, [soundFileName]);

  const setEnabled = useCallback((enabled: boolean) => {
    setIsEnabled(enabled);
  }, []);

  return {
    playSound,
    isLoaded,
    isEnabled,
    setEnabled,
  };
};
