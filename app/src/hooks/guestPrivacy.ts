import storage from '@react-native-async-storage/async-storage';
import { useEffect, useState } from 'react';

interface GuestPrivacy {
  loading: boolean;
  guestPrivacyConsent: boolean;
  setGuestPrivacyConsent: (consent: boolean) => Promise<void>;
}

export enum PulseGuestConsents {
  BPPULSE_GUEST_PRIVACY = 'BPPULSE_GUEST_PRIVACY',
}

export function useGuestConsents(): GuestPrivacy {
  const [loading, setLoading] = useState(true);
  const [guestPrivacyConsent, setGuestPrivacyConsent] = useState(false);

  const setConsent = async (consent: boolean) =>
    storage
      .setItem(
        PulseGuestConsents.BPPULSE_GUEST_PRIVACY,
        consent ? 'true' : 'false',
      )
      .then(() => setGuestPrivacyConsent(consent));

  useEffect(() => {
    (async () => {
      const val = await storage.getItem(
        PulseGuestConsents.BPPULSE_GUEST_PRIVACY,
      );

      setGuestPrivacyConsent(val === 'true');
      setLoading(false);
    })();
  }, []);

  return {
    loading,
    guestPrivacyConsent,
    setGuestPrivacyConsent: setConsent,
  };
}
