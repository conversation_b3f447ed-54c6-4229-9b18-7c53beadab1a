import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { PushNotificationConsentService } from '../analytics/handlers/PushNotificationConsentService';
import { PermissionStatus } from '@ua/react-native-airship';

/**
 * Test component for push notifications
 * This component can be temporarily added to any screen for testing
 */
export const PushNotificationTestComponent: React.FC = () => {
  const [permissionStatus, setPermissionStatus] = useState<string>('Loading...');
  const [isLoading, setIsLoading] = useState(false);

  const pnService = new PushNotificationConsentService();

  // Check current permission status
  const checkPermissionStatus = async () => {
    try {
      const status = await pnService.nativePermissionStatus();
      const statusText = status.notificationPermissionStatus;
      setPermissionStatus(statusText);

      console.log('Full notification status:', JSON.stringify(status, null, 2));
    } catch (error) {
      console.error('Error checking permission status:', error);
      setPermissionStatus('Error');
    }
  };

  // Request push notification permission
  const requestPermission = async () => {
    setIsLoading(true);
    try {
      const granted = await pnService.requestPushNotificationPermission();

      if (granted) {
        Alert.alert('Success', 'Push notifications enabled!');
      } else {
        Alert.alert('Permission Denied', 'Push notifications were not enabled. Please check your device settings.');
      }

      // Refresh status
      await checkPermissionStatus();
    } catch (error) {
      console.error('Error requesting permission:', error);
      Alert.alert('Error', 'Failed to request push notification permission');
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user has allowed push notifications
  const checkUserAllowed = async () => {
    try {
      const isAllowed = await pnService.isPushNotificationAllowedByUser();
      Alert.alert(
        'Permission Status',
        `Push notifications ${isAllowed ? 'are' : 'are not'} allowed by user`
      );
    } catch (error) {
      console.error('Error checking user permission:', error);
      Alert.alert('Error', 'Failed to check user permission');
    }
  };

  // Load initial status
  useEffect(() => {
    checkPermissionStatus();
  }, []);

  const getStatusColor = () => {
    switch (permissionStatus) {
      case PermissionStatus.Granted:
        return '#4CAF50'; // Green
      case PermissionStatus.Denied:
        return '#F44336'; // Red
      case PermissionStatus.NotDetermined:
        return '#FF9800'; // Orange
      default:
        return '#9E9E9E'; // Gray
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Push Notification Test</Text>

      <View style={styles.statusContainer}>
        <Text style={styles.statusLabel}>Permission Status:</Text>
        <Text style={[styles.statusText, { color: getStatusColor() }]}>
          {permissionStatus}
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.button}
          onPress={requestPermission}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? 'Requesting...' : 'Request Permission'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={checkPermissionStatus}
        >
          <Text style={styles.buttonText}>Refresh Status</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={checkUserAllowed}
        >
          <Text style={styles.buttonText}>Check User Allowed</Text>
        </TouchableOpacity>
      </View>

      <Text style={styles.instructions}>
        Use this component to test push notification permissions.{'\n'}
        Check the console logs for detailed information.{'\n'}
        {'\n'}
        Expected flow:{'\n'}
        1. Request Permission → User grants/denies{'\n'}
        2. Send test notification from Airship dashboard{'\n'}
        3. Notification should appear even in foreground{'\n'}
        {'\n'}
        🔊 Custom Sound Configuration:{'\n'}
        • Android: Use {'"sound": "charge_start"'} in payload{'\n'}
        • iOS: Use {'"sound": "charge_start.mp3"'} in payload{'\n'}
        • See docs/CUSTOM_NOTIFICATION_SOUNDS.md for details
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f0f0f0',
    margin: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    justifyContent: 'center',
  },
  statusLabel: {
    fontSize: 16,
    marginRight: 10,
  },
  statusText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonContainer: {
    marginBottom: 15,
  },
  button: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 5,
    marginBottom: 10,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  instructions: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
