import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useConnectivity } from '@providers/ConnectivityProvider';
import { useAuth } from '@bp/pulse-auth-sdk';
import env from '@env';

/**
 * Network debugging component to test Apollo connectivity
 * This component can be temporarily added to any screen for testing
 */
export const NetworkDebugComponent: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { isInternetReachable } = useConnectivity();
  const { authenticated, getIdToken } = useAuth();

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testBasicConnectivity = async () => {
    setIsLoading(true);
    addResult('🔍 Testing basic connectivity...');
    
    try {
      // Test basic internet connectivity
      const response = await fetch('https://www.google.com', {
        method: 'HEAD',
        timeout: 5000,
      });
      
      if (response.ok) {
        addResult('✅ Basic internet connectivity: OK');
      } else {
        addResult(`❌ Basic internet connectivity: Failed (${response.status})`);
      }
    } catch (error) {
      addResult(`❌ Basic internet connectivity: Error - ${error.message}`);
    }
    
    setIsLoading(false);
  };

  const testGraphQLEndpoints = async () => {
    setIsLoading(true);
    addResult('🔍 Testing GraphQL endpoints...');
    
    const endpoints = [
      { name: 'Public Gateway', url: env.GATEWAY_URL_PUBLIC },
      { name: 'Private Gateway', url: env.GATEWAY_URL_PRIVATE },
    ];

    for (const endpoint of endpoints) {
      try {
        addResult(`Testing ${endpoint.name}: ${endpoint.url}`);
        
        const response = await fetch(endpoint.url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': env.API_GATEWAY_KEY,
            'x-app-name': 'bppulse',
            'x-app-version': '1.0.0',
          },
          body: JSON.stringify({
            query: 'query { __typename }',
          }),
          timeout: 10000,
        });

        if (response.ok) {
          const data = await response.json();
          addResult(`✅ ${endpoint.name}: OK - ${JSON.stringify(data)}`);
        } else {
          const errorText = await response.text();
          addResult(`❌ ${endpoint.name}: Failed (${response.status}) - ${errorText}`);
        }
      } catch (error) {
        addResult(`❌ ${endpoint.name}: Error - ${error.message}`);
      }
    }
    
    setIsLoading(false);
  };

  const testAuthToken = async () => {
    setIsLoading(true);
    addResult('🔍 Testing authentication token...');
    
    try {
      if (authenticated) {
        const token = await getIdToken();
        if (token) {
          addResult(`✅ Auth token: Available (${token.substring(0, 20)}...)`);
        } else {
          addResult('❌ Auth token: Not available');
        }
      } else {
        addResult('ℹ️ Auth token: Not authenticated (using public endpoint)');
      }
    } catch (error) {
      addResult(`❌ Auth token: Error - ${error.message}`);
    }
    
    setIsLoading(false);
  };

  const runAllTests = async () => {
    clearResults();
    addResult('🚀 Starting network diagnostics...');
    addResult(`📱 Internet reachable: ${isInternetReachable}`);
    addResult(`🔐 Authenticated: ${authenticated}`);
    addResult(`🌐 API Gateway Key: ${env.API_GATEWAY_KEY.substring(0, 10)}...`);
    
    await testBasicConnectivity();
    await testGraphQLEndpoints();
    await testAuthToken();
    
    addResult('✅ Network diagnostics completed');
  };

  const showResults = () => {
    Alert.alert(
      'Network Test Results',
      testResults.join('\n'),
      [{ text: 'OK' }],
      { cancelable: true }
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Network Debug Tool</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusLabel}>Status:</Text>
        <Text style={[styles.statusText, { color: isInternetReachable ? '#4CAF50' : '#F44336' }]}>
          {isInternetReachable ? 'Connected' : 'Disconnected'}
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.button}
          onPress={runAllTests}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? 'Testing...' : 'Run All Tests'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={testBasicConnectivity}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Basic Connectivity</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={testGraphQLEndpoints}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test GraphQL Endpoints</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={showResults}
          disabled={testResults.length === 0}
        >
          <Text style={styles.buttonText}>Show Results ({testResults.length})</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearResults}
        >
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>

      <Text style={styles.instructions}>
        Use this tool to diagnose Apollo network connectivity issues.{'\n'}
        Results will show in console and can be viewed via "Show Results".{'\n'}
        {'\n'}
        Current endpoints:{'\n'}
        • Public: {env.GATEWAY_URL_PUBLIC}{'\n'}
        • Private: {env.GATEWAY_URL_PRIVATE}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f0f0f0',
    margin: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    justifyContent: 'center',
  },
  statusLabel: {
    fontSize: 16,
    marginRight: 10,
  },
  statusText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonContainer: {
    marginBottom: 15,
  },
  button: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 5,
    marginBottom: 10,
  },
  clearButton: {
    backgroundColor: '#FF6B6B',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  instructions: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
