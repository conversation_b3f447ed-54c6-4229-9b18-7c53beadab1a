import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useNotificationSound } from '../hooks/useNotificationSound';

/**
 * Test component for manually testing notification sounds
 * This component can be temporarily added to any screen for testing
 */
export const SoundTestComponent: React.FC = () => {
  const { playSound, isLoaded, isEnabled, setEnabled } = useNotificationSound({
    soundFileName: 'notification_158187',
    volume: 0.8,
    enabled: true,
  });

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Sound Test Component</Text>

      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Sound Status: {isLoaded ? '✅ Loaded' : '❌ Not Loaded'}
        </Text>
        <Text style={styles.statusText}>
          Sound Enabled: {isEnabled ? '✅ Enabled' : '❌ Disabled'}
        </Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, !isLoaded && styles.buttonDisabled]}
          onPress={playSound}
          disabled={!isLoaded}
        >
          <Text style={styles.buttonText}>Play Sound</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={() => setEnabled(!isEnabled)}
        >
          <Text style={styles.buttonText}>
            {isEnabled ? 'Disable' : 'Enable'} Sound
          </Text>
        </TouchableOpacity>
      </View>

      <Text style={styles.instructions}>
        Use this component to test notification sounds.{'\n'}
        The sound should play when you tap "Play Sound".{'\n'}
        Check the console for loading status and error messages.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f0f0f0',
    margin: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  statusContainer: {
    marginBottom: 15,
  },
  statusText: {
    fontSize: 14,
    marginBottom: 5,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 15,
  },
  button: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  instructions: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
