import { WifiOff } from '@assets/images';
import { useCharge } from '@bp/charge-mfe';
import { SimpleNotification } from '@bp/ui-components/mobile/core';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useConnectivity } from '../../providers/ConnectivityProvider';
import { useNotificationSound } from '../../hooks/useNotificationSound';

const Notifications = () => {
  const { isCharging: isChargingHook } = useCharge();
  const { isInternetReachable } = useConnectivity();
  const { t } = useTranslation();

  const [isConnected, setIsConnected] = useState(true);
  const [isCharging, setIsCharging] = useState(false);

  // Use the custom notification sound hook
  const { playSound: playNotificationSound } = useNotificationSound({
    soundFileName: 'notification_158187',
    volume: 0.8,
    enabled: true,
  });

  useEffect(() => {
    if (isChargingHook !== isCharging) {
      setIsCharging(isChargingHook);

      // Play sound when charging starts
      if (isChargingHook) {
        playNotificationSound();
      }
    }

    if (isInternetReachable !== isConnected) {
      setIsConnected(isInternetReachable);

      // Play sound when connection status changes (lost connection)
      if (!isInternetReachable) {
        playNotificationSound();
      }
    }
  }, [isInternetReachable, isChargingHook, isCharging, isConnected, playNotificationSound]);

  if (!isConnected && !isCharging) {
    return (
      <SimpleNotification
        title={t('notifications.lostConnection.title')}
        icon={<WifiOff />}
      />
    );
  }

  if (!isConnected && isCharging) {
    return (
      <SimpleNotification
        icon={<WifiOff />}
        title={t('notifications.lostConnection.title')}
        text={t('notifications.lostConnection.text')}
      />
    );
  }

  return null;
};

export default Notifications;
