// @ts-nocheck
import { useCharge } from '@bp/charge-mfe';
import { render, screen } from '@testing-library/react-native';
import React from 'react';

import { useConnectivity } from '../../providers/ConnectivityProvider';
import { useNotificationSound } from '../../hooks/useNotificationSound';
import Notifications from './Notifications';

jest.mock('@bp/charge-mfe', () => ({
  useCharge: jest.fn(() => ({ isCharging: false })),
}));

jest.mock('../../providers/ConnectivityProvider', () => ({
  useConnectivity: jest.fn(() => ({ isInternetReachable: true })),
}));

jest.mock('../../hooks/useNotificationSound', () => ({
  useNotificationSound: jest.fn(() => ({
    playSound: jest.fn(),
    isLoaded: true,
    isEnabled: true,
    setEnabled: jest.fn(),
  })),
}));

describe('<Notifications />', () => {
  let mockPlaySound;

  beforeEach(() => {
    mockPlaySound = jest.fn();
    useNotificationSound.mockReturnValue({
      playSound: mockPlaySound,
      isLoaded: true,
      isEnabled: true,
      setEnabled: jest.fn(),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders nothing if there is no network interruption', () => {
    render(<Notifications />);

    const toast = screen.queryByTestId('toast');
    expect(toast).toBeNull(); // it doesn't exist
  });

  it('renders a simple notification if there is no internet connection', () => {
    useConnectivity.mockImplementation(() => ({ isInternetReachable: false }));

    render(<Notifications />);
    const toast = screen.queryByTestId('toast');

    expect(toast).toBeDefined();
  });

  it('renders a special notification if there is no internet connection, and a charge in progress', () => {
    useConnectivity.mockImplementation(() => ({ isInternetReachable: false }));
    useCharge.mockImplementation(() => ({ isCharging: true }));
    render(<Notifications />);

    expect(screen.queryByTestId('toast')).toBeDefined();
    expect(screen.queryByText('notification.text')).toBeDefined();
  });

  it('initializes notification sound hook with correct parameters', () => {
    render(<Notifications />);

    expect(useNotificationSound).toHaveBeenCalledWith({
      soundFileName: 'charge_start',
      volume: 0.8,
      enabled: true,
    });
  });

  it('plays sound when charging starts', () => {
    // Start with charging false
    useCharge.mockImplementation(() => ({ isCharging: false }));
    const { rerender } = render(<Notifications />);

    // Change to charging true
    useCharge.mockImplementation(() => ({ isCharging: true }));
    rerender(<Notifications />);

    expect(mockPlaySound).toHaveBeenCalled();
  });

  it('plays sound when internet connection is lost', () => {
    // Start with connection true
    useConnectivity.mockImplementation(() => ({ isInternetReachable: true }));
    const { rerender } = render(<Notifications />);

    // Change to connection false
    useConnectivity.mockImplementation(() => ({ isInternetReachable: false }));
    rerender(<Notifications />);

    expect(mockPlaySound).toHaveBeenCalled();
  });
});
