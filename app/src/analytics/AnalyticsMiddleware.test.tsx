// @ts-nocheck

import { AirshipAnalyticsService } from '@analytics/handlers/airship';
import * as AppsFlyer from '@analytics/handlers/appsflyer';
import * as Firebase from '@analytics/handlers/firebase';
import { PushNotificationConsentService } from '@analytics/handlers/PushNotificationConsentService';
import { useAppSettings } from '@bp/profile-mfe';
import { UserTypes } from '@bp/profile-mfe/dist/common/enums';
import { useAuth } from '@bp/pulse-auth-sdk';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { waitFor } from '@testing-library/react-native';
import { logger } from '@utils/logger';
import { render } from '@utils/test-utils';
import React from 'react';
import * as ATT from 'react-native-tracking-transparency';
import { requestTrackingPermission } from 'react-native-tracking-transparency';

import AnalyticsMiddleware from './AnalyticsMiddleware';

//Mock Airship platform
jest.mock('@ua/react-native-airship', () => {
  const PermissionStatus = {
    Granted: 'granted',
    Denied: 'denied',
    NotDetermined: 'not_determined',
  };

  // Create the mock object
  return {
    takeOff: jest.fn().mockResolvedValue(true),
    PermissionStatus,
    push: {
      setUserNotificationsEnabled: jest.fn().mockResolvedValue(true),
      getNotificationStatus: jest.fn().mockResolvedValue({
        notificationPermissionStatus: PermissionStatus.Granted,
        isPushPrivacyFeatureEnabled: true,
        isUserNotificationsEnabled: true,
        isOptedIn: true,
        isBackgroundPushEnabled: true,
      }),
      iOS: {
        setNotificationOptions: jest.fn(),
      },
    },
    iOS: {
      NotificationOption: {
        Alert: 'alert',
        Badge: 'badge',
        Sound: 'sound',
      },
    },
    contact: {
      identify: jest.fn().mockResolvedValue(true),
      getNamedUserId: jest.fn().mockResolvedValue('mocked-named-user-id'),
    },
    channel: {
      getChannelId: jest.fn().mockResolvedValue('mocked-channel-id'),
      editAttributes: jest.fn(),
    },
    analytics: {
      addCustomEvent: jest.fn(() => ({
        eventName: 'home_country',
        properties: { country: 'UK' },
      })),
    },
    on: jest.fn(),
    removeListener: jest.fn(),
    airshipListenerAdded: false,
  };
});
// Mock userInfo
jest.mock('@bp/profile-mfe', () => ({
  useAppSettings: jest.fn(),
}));

// Mock user
jest.mock('@bp/pulse-auth-sdk', () => ({
  useAuth: jest.fn(),
}));

const mockUserScheme = {
  userInfo: {
    userScheme: [
      {
        schemeName: 'schemeName',
      },
    ],
    partnerType: 'uber',
    userType: UserTypes.SUBS_WALLET,
    migrationUserStatus: 'MANDATORY',
  },
};
const mockNoScheme = {
  userInfo: {
    userScheme: null,
  },
};
const mockUser = {
  userId: 'userId',
  locale: 'locale',
};
const mockCountry = 'UK';

jest.mock('react-native-tracking-transparency', () => ({
  requestTrackingPermission: jest.fn(),
}));

jest.mock('@analytics/handlers/firebase', () => ({
  init: jest.fn(),
}));

jest.mock('@analytics/handlers/appsflyer', () => ({
  initSdk: jest.fn(),
}));

jest.mock('@analytics/handlers/airship', () => ({
  AirshipAnalyticsService: {
    initAirShip: jest.fn(),
    createCustomEvent: jest.fn(() => ({
      eventName: 'home_country',
      properties: { country: 'UK' },
    })),
    disablePushNotificationInAirship: jest.fn(),
    enablePushNotificationInAirship: jest.fn(),
    associateUserIdToAirshipNamedUserId: jest.fn(),
    setCustomerTypeAttribute: jest.fn(),
    setMigrationUserStatusAttribute: jest.fn(),
    setSalesforcePushConsentAttribute: jest.fn(),
  },
  logCustomEvent: jest.fn(),
  analytics: {
    addCustomEvent: jest.fn(),
  },
  contact: {
    identify: jest.fn().mockResolvedValue(true),
  },
  channel: {
    getChannelId: jest.fn().mockResolvedValue('mockChannelId'),
  },
}));

jest.mock('@analytics/handlers/PushNotificationConsentService');

const mockGetConsents = jest.fn().mockResolvedValue([
  {
    accepted: true,
    acceptedTime: '2025-04-03T08:37:33.000Z',
    appName: 'chargemaster',
    channel: null,
    consentId: 'a0LUE0000058Zsc2AE',
    consentType: 'Track and Target',
    identityExternalId: '6f586011-29e1-47c1-824d-9dad4b12ec08',
    version: null,
  },
]);

useAuth.mockImplementation(() => ({
  user: { userId: 'test-user-id' },
  authenticated: true,
  getConsents: mockGetConsents,
  updateTrackingConsent: jest.fn(),
}));

useAppSettings.mockImplementation(() => mockUserScheme);

describe('<AnalyticsMiddleware />', () => {
  let spy: jest.SpyInstance;
  beforeEach(() => {
    spy = jest.spyOn(console, 'warn').mockImplementation(() => null);
  });
  afterEach(() => {
    spy.mockRestore();
    jest.clearAllMocks();
  });

  it("requests the iOS user's consent to ATT tracking, on app load", async () => {
    useAuth.mockImplementation(() => ({
      user: mockUser,
      updateTrackingConsent: jest.fn(),
      getUser: jest
        .fn()
        .mockResolvedValue({ sfContactId: 'mock-sf-contact-id' }),
    }));

    render(<AnalyticsMiddleware />);

    await waitFor(() => {
      expect(ATT.requestTrackingPermission).toBeCalledTimes(1);
    });
  });

  it('logs any errors in requesting ATT consent', () => {
    useAuth.mockImplementation(() => ({
      user: mockUser,
      updateTrackingConsent: jest.fn(),
      getUser: jest
        .fn()
        .mockResolvedValue({ sfContactId: 'mock-sf-contact-id' }),
    }));
    const logSpy = jest.spyOn(logger, 'warn');

    // Mock error thrown
    requestTrackingPermission.mockImplementation(() => {
      throw new Error();
    });

    render(<AnalyticsMiddleware />);

    // Expect logger called with error
    expect(logSpy).toHaveBeenCalledWith(
      'Error in requestAttConsent: ',
      'Error',
    );
  });

  it('calls updateTrackingConsent with true, if the user is logged in and consented to ATT', async () => {
    const mockUpdate = jest.fn();

    // Setup PushNotificationConsentService mock since these tests are not in the proper describe block
    const mockPnConsentServiceForATT = {
      isSalesForcePushConsentAccepted: jest.fn().mockResolvedValue(true),
      nativePermissionStatus: jest.fn().mockResolvedValue({
        notificationPermissionStatus: 'granted',
      }),
      isPushNotificationAllowedByUser: jest.fn().mockResolvedValue(true),
    };

    PushNotificationConsentService.mockImplementation(
      () => mockPnConsentServiceForATT,
    );

    useAuth.mockImplementation(() => ({
      user: mockUser,
      authenticated: true,
      updateTrackingConsent: mockUpdate,
      getConsents: mockGetConsents,
      getUser: jest
        .fn()
        .mockResolvedValue({ sfContactId: 'mock-sf-contact-id' }),
    }));
    requestTrackingPermission.mockResolvedValue('authorized');

    render(<AnalyticsMiddleware />);

    await waitFor(() => expect(mockUpdate).toBeCalledWith(true), {
      timeout: 3000,
    });
  });

  it('calls updateTrackingConsent with false, if the user is logged in and did not consent to ATT', async () => {
    const mockUpdate = jest.fn();

    // Setup PushNotificationConsentService mock since these tests are not in the proper describe block
    const mockPnConsentServiceForATT = {
      isSalesForcePushConsentAccepted: jest.fn().mockResolvedValue(false),
      nativePermissionStatus: jest.fn().mockResolvedValue({
        notificationPermissionStatus: 'granted',
      }),
      isPushNotificationAllowedByUser: jest.fn().mockResolvedValue(true),
    };

    PushNotificationConsentService.mockImplementation(
      () => mockPnConsentServiceForATT,
    );

    useAuth.mockImplementation(() => ({
      user: mockUser,
      authenticated: true,
      updateTrackingConsent: mockUpdate,
      getConsents: mockGetConsents,
      getUser: jest
        .fn()
        .mockResolvedValue({ sfContactId: 'mock-sf-contact-id' }),
    }));
    requestTrackingPermission.mockResolvedValue('denied');

    render(<AnalyticsMiddleware />);

    await waitFor(() => expect(mockUpdate).toBeCalledWith(false), {
      timeout: 3000,
    });
  });

  it('does not initialise analytics, if the iOS user has not authenticated nor consented to ATT', async () => {
    const mockUpdate = jest.fn();
    useAuth.mockImplementation(() => ({
      user: mockUser,
      authenticated: false,
      updateTrackingConsent: mockUpdate,
      getUser: jest
        .fn()
        .mockResolvedValue({ sfContactId: 'mock-sf-contact-id' }),
    }));

    // Mock anything but 'authorized'
    requestTrackingPermission.mockResolvedValue('denied');

    render(<AnalyticsMiddleware />);

    await waitFor(() => expect(mockUpdate).not.toBeCalled());
    expect(AppsFlyer.initSdk).not.toBeCalled();
    expect(AirshipAnalyticsService.initAirShip).not.toBeCalled();
  });

  it('initialises analytics if iOS user consents to ATT tracking', async () => {
    useAuth.mockImplementation(() => ({
      user: mockUser,
      authenticated: false,
      updateTrackingConsent: jest.fn(),
      getUser: jest
        .fn()
        .mockResolvedValue({ sfContactId: 'mock-sf-contact-id' }),
    }));
    requestTrackingPermission.mockResolvedValue('authorized');

    render(<AnalyticsMiddleware />);

    await waitFor(() => {
      expect(AppsFlyer.initSdk).toBeCalledTimes(1);
    });
  });

  it('initialises analytics if user logs in/registers', async () => {
    // Setup PushNotificationConsentService mock since these tests are not in the proper describe block
    const mockPnConsentServiceForInit = {
      isSalesForcePushConsentAccepted: jest.fn().mockResolvedValue(true),
      nativePermissionStatus: jest.fn().mockResolvedValue({
        notificationPermissionStatus: 'granted',
      }),
      isPushNotificationAllowedByUser: jest.fn().mockResolvedValue(true),
    };

    PushNotificationConsentService.mockImplementation(
      () => mockPnConsentServiceForInit,
    );

    useAuth.mockImplementation(() => ({
      user: mockUser,
      authenticated: true,
      updateTrackingConsent: jest.fn(),
      getConsents: mockGetConsents,
      getUser: jest
        .fn()
        .mockResolvedValue({ sfContactId: 'mock-sf-contact-id' }),
    }));
    requestTrackingPermission.mockResolvedValue('denied');

    render(<AnalyticsMiddleware />);

    await waitFor(
      () => {
        expect(AppsFlyer.initSdk).toBeCalledTimes(1);
        expect(Firebase.init).toBeCalledTimes(1);
        expect(AirshipAnalyticsService.initAirShip).toBeCalled();
      },
      { timeout: 3000 },
    );
  });

  it('initialises analytics if user is on Android device', async () => {
    useAuth.mockImplementation(() => ({
      user: mockUser,
      authenticated: false,
      updateTrackingConsent: jest.fn(),
      getUser: jest
        .fn()
        .mockResolvedValue({ sfContactId: 'mock-sf-contact-id' }),
    }));
    jest.mock('react-native/Libraries/Utilities/Platform', () => ({
      OS: 'android', // or 'ios'
      select: () => null,
    }));
    requestTrackingPermission.mockResolvedValue('unavailable');

    render(<AnalyticsMiddleware />);

    await waitFor(() => {
      expect(AppsFlyer.initSdk).toBeCalledTimes(1);
      expect(Firebase.init).toBeCalledTimes(1);
      expect(AirshipAnalyticsService.initAirShip).toBeCalled();
    });
  });

  it('Updates AsyncStorage with user details pulled from various hooks', async () => {
    useAuth.mockImplementation(() => ({
      user: mockUser,
      updateTrackingConsent: jest.fn(),
      getUser: jest
        .fn()
        .mockResolvedValue({ sfContactId: 'mock-sf-contact-id' }),
    }));

    render(<AnalyticsMiddleware />);

    await waitFor(() =>
      expect(AsyncStorage.setItem).toBeCalledWith('@country', mockCountry),
    );

    expect(AsyncStorage.setItem).toBeCalledWith('@user_id', mockUser.userId);
    expect(AsyncStorage.setItem).toBeCalledWith('@locale', mockUser.locale);
    expect(AsyncStorage.setItem).toBeCalledWith(
      '@user_scheme',
      mockUserScheme.userInfo.userScheme.map(s => s.schemeName).toString(),
    );
    expect(AsyncStorage.setItem).toBeCalledWith(
      '@partner_type',
      mockUserScheme.userInfo.partnerType,
    );

    expect(AsyncStorage.setItem).toBeCalledWith(
      '@user_type',
      mockUserScheme.userInfo.userType,
    );
  });

  it('Sets AsyncStorage userScheme & partnerType & userType to an empty string if the value is null', async () => {
    useAuth.mockImplementation(() => ({
      user: mockUser,
      updateTrackingConsent: jest.fn(),
      getUser: jest
        .fn()
        .mockResolvedValue({ sfContactId: 'mock-sf-contact-id' }),
    }));
    useAppSettings.mockImplementation(() => mockNoScheme);

    render(<AnalyticsMiddleware />);

    await waitFor(() =>
      expect(AsyncStorage.setItem).toBeCalledWith('@user_scheme', ''),
    );

    await waitFor(() =>
      expect(AsyncStorage.setItem).toBeCalledWith('@partner_type', ''),
    );

    await waitFor(() =>
      expect(AsyncStorage.setItem).toBeCalledWith('@user_type', ''),
    );
  });
});

describe('checkUserNotificationConsent', () => {
  let mockPnConsentService;
  let mockGetConsents;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock PushNotificationConsentService behavior
    mockPnConsentService = {
      isSalesForcePushConsentAccepted: jest.fn(),
      nativePermissionStatus: jest.fn().mockResolvedValue({
        notificationPermissionStatus: 'granted',
      }),
      isPushNotificationAllowedByUser: jest.fn().mockResolvedValue(true),
    };

    PushNotificationConsentService.mockImplementation(
      () => mockPnConsentService,
    );

    // Mock useAuth return values for this test section
    mockGetConsents = jest.fn();
    useAuth.mockReturnValue({
      authenticated: true,
      user: { userId: 'test-user-id' },
      getConsents: mockGetConsents,
      updateTrackingConsent: jest.fn(),
      getUser: jest
        .fn()
        .mockResolvedValue({ sfContactId: 'mock-sf-contact-id' }),
    });
  });

  it('should not set customer Type Attribute to Airship when user is not Authenticated', async () => {
    useAuth.mockReturnValue({
      authenticated: false,
    });
    render(<AnalyticsMiddleware />);
    await waitFor(() => {
      expect(
        AirshipAnalyticsService.setCustomerTypeAttribute,
      ).not.toHaveBeenCalled();
    });
  });
  it('should set migration user status attribute to Airship when user is Authenticated', async () => {
    useAppSettings.mockImplementation(() => mockUserScheme);
    render(<AnalyticsMiddleware />);
    await waitFor(() => {
      expect(
        AirshipAnalyticsService.setMigrationUserStatusAttribute,
      ).toHaveBeenCalledWith(mockUserScheme.userInfo.migrationUserStatus);
    });
  });
  it('should not set migration user status attribute to Airship when user is not Authenticated', async () => {
    require('@bp/pulse-auth-sdk').useAuth.mockReturnValue({
      authenticated: false,
    });
    render(<AnalyticsMiddleware />);
    await waitFor(() => {
      expect(
        AirshipAnalyticsService.setMigrationUserStatusAttribute,
      ).not.toHaveBeenCalled();
    });
  });
});
