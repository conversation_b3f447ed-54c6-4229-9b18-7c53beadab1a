/**
 * AnalyticsMiddleware manages privacy consents and
 * storing user info for use in analytics platforms
 *
 * - Initialises all platform analytics if:
 *    - iOS v14+ user consents to ATT tracking
 *    - Any user registers/logs in (authenticates)
 * - Enables populating analyticsEvent() with user info by saving it to Async Storage
 * - Sends tracking consent response to Salesforce
 */

import {
  AirshipAnalyticsService,
  logCustomEvent,
} from '@analytics/handlers/airship';
import * as AppsFlyer from '@analytics/handlers/appsflyer';
import * as Firebase from '@analytics/handlers/firebase';
import { useOnboarding } from '@bp/onboarding-mfe';
import { useAppSettings } from '@bp/profile-mfe';
import { useAuth } from '@bp/pulse-auth-sdk';
import { setAsyncStorageItem } from '@common/asyncStorage';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Airship from '@ua/react-native-airship';
import { logger } from '@utils/logger';
import { checkUserNotificationConsent } from '@utils/pnConsentHelper';
import { useCallback, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import {
  requestTrackingPermission,
  TrackingStatus,
} from 'react-native-tracking-transparency';

const AnalyticsMiddleware = () => {
  const { user, authenticated, getConsents, updateTrackingConsent } = useAuth();
  const { userInfo } = useAppSettings();
  const {
    onboardingStatus: { country },
  } = useOnboarding();

  // ATT Tracking
  const [trackingStatus, setTrackingStatus] = useState<
    TrackingStatus | '(loading)'
  >('(loading)');
  // Analytics initialised
  const [isInitialised, setInitialised] = useState(false);
  const isAndroid = Platform.OS === 'android';

  // Checks ATT permission, and presents modal to user if status == "not-determined"
  const requestAttConsent = useCallback(async () => {
    try {
      const status = await requestTrackingPermission();

      setTrackingStatus(status);
    } catch (e) {
      logger.warn('Error in requestAttConsent: ', e?.toString?.() ?? e);
    }
  }, []);

  // Initialise all analytics platforms (once)
  const initAnalytics = useCallback(async () => {
    if (!isInitialised) {
      AppsFlyer.initSdk();
      AirshipAnalyticsService.initAirShip();

      setInitialised(true);
    }
  }, [isInitialised]);

  // Check ATT permissions on app load
  useEffect(() => {
    requestAttConsent();
  }, [requestAttConsent]);

  const runUpdateTrackingConsent = useCallback(
    (status: TrackingStatus) => {
      updateTrackingConsent(
        status === 'authorized' || status === 'unavailable',
      );
    },
    [updateTrackingConsent],
  );

  useEffect(() => {
    // Sets Salesforce "track and target" consent when user authenticates
    if (trackingStatus !== '(loading)' && authenticated) {
      runUpdateTrackingConsent(trackingStatus);
    }
    const runAirshipTasks = async () => {
      if (Airship.contact && Airship.channel.getChannelId) {
        const channelId = await Airship.channel.getChannelId();
        logCustomEvent('channel_id', { channelId: channelId });
        logger.log('Channel ID:', channelId);
      } else {
        logger.error('Airship.contact or getChannelId is undefined!');
      }
      await Airship.contact.getNamedUserId();
    };
    //firebase init irrespective of the user's ATT consent
    if (!isInitialised) {
      Firebase.init(true);
    }
    /* Turn on analytics when:
     *  - Android user
     *  - iOS user who has consented to ATT
     *  - or - any authenticated user
     */
    if (isAndroid || trackingStatus === 'authorized' || authenticated) {
      initAnalytics();
      runAirshipTasks();
    }
    /*
    We don't want this to run everytime runUpdateTrackingConsent updates as it creates a render loop
    This should be resolved by refactoring updateTrackingConsent in the sfidp mfe
    */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authenticated, isAndroid, trackingStatus, initAnalytics]);

  // Update Async storage when values change
  useEffect(() => {
    if (country) {
      AsyncStorage.setItem('@country', country);
      logCustomEvent('home_country', { country: country });
    }
  }, [country]);

  // Update Async storage when values change
  useEffect(() => {
    if (user?.userId) {
      setAsyncStorageItem('@user_id', user?.userId);
      logCustomEvent('customer_id', { userId: user.userId });
    }
    if (user?.locale) {
      setAsyncStorageItem('@locale', user?.locale);
      logCustomEvent('locale', { locale: user.locale });
    }
  }, [user]);

  // Update Async storage when values change
  useEffect(() => {
    setAsyncStorageItem('@user_migration_date', userInfo.userMigrationDate);
    setAsyncStorageItem(
      '@user_scheme',
      userInfo.userScheme?.map(scheme => scheme.schemeName).toString(),
    );
    setAsyncStorageItem('@partner_type', userInfo.partnerType);
    setAsyncStorageItem('@user_type', userInfo.userType);
  }, [userInfo]);

  useEffect(() => {
    checkUserNotificationConsent(authenticated, getConsents);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authenticated]);

  useEffect(() => {
    if (authenticated && user) {
      AirshipAnalyticsService.associateUserIdToAirshipNamedUserId(user.userId);
      AirshipAnalyticsService.setCustomerTypeAttribute(userInfo.userType);
      AirshipAnalyticsService.setMigrationUserStatusAttribute(
        userInfo.migrationUserStatus,
      );
    }
  }, [authenticated, user, userInfo]);

  return null;
};

export default AnalyticsMiddleware;
