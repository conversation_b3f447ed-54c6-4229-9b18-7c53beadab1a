# Custom Notification Sounds Configuration

This guide explains how to configure custom notification sounds for both Android and iOS using Airship push notifications.

## Overview

The app uses `notification_158187` as the custom notification sound. This sound is configured differently for Android and iOS platforms.

## Android Configuration

### ✅ Files Already Configured
- **Sound file**: `android/app/src/main/res/raw/notification_158187` ✅
- **Notification icon**: `android/app/src/main/res/drawable/ic_notification.xml` ✅
- **Airship config**: Default channel ID set to `bp_pulse_notifications` ✅
- **Notification channel**: Created in `MainApplication.java` with custom sound ✅

### 🔧 Implementation Details

The custom notification sound is now automatically configured through:

1. **Notification Channel Creation**: The `MainApplication.java` creates a notification channel with the custom sound during app initialization
2. **Channel ID Matching**: The channel ID `bp_pulse_notifications` matches the Airship configuration
3. **Automatic Sound**: All notifications sent through Airship will automatically use the custom sound

### 🔧 Airship Dashboard Configuration

No additional configuration needed for Android - the custom sound is automatically applied.
For reference, the notification payload structure is:

```json
{
  "notification": {
    "alert": "Your notification message",
    "android": {
      "sound": "charge_start"
    }
  }
}
```

**Note**: For Android, use the filename WITHOUT the `.mp3` extension.

## iOS Configuration

### ⚠️ Files Need to be Added
- **Sound file**: Needs to be added to iOS bundle via Xcode
- **Xcode configuration**: Sound file must be added to project targets

### 📱 iOS Setup Steps

1. **Add sound file to Xcode**:
   ```bash
   # Copy the sound file to iOS resources
   cp android/app/src/main/res/raw/notification_158187 ios/notification_158187
   ```

2. **Open Xcode**:
   ```bash
   cd ios
   open bppulse.xcworkspace
   ```

3. **Add file to project**:
   - Right-click on project in Xcode
   - Select "Add Files to 'bppulse'"
   - Choose `notification_158187`
   - Ensure it's added to both app targets

4. **Verify bundle inclusion**:
   - Select the sound file in Xcode
   - Check "Target Membership" in right panel
   - Ensure both targets are checked

### 🔧 Airship Dashboard Configuration for iOS

```json
{
  "notification": {
    "alert": "Your notification message",
    "ios": {
      "sound": "notification_158187"
    }
  }
}
```

**Note**: For iOS, use the filename WITH the `.mp3` extension.

## Combined Configuration

For notifications that work on both platforms:

```json
{
  "notification": {
    "alert": "Your notification message",
    "android": {
      "sound": "charge_start"
    },
    "ios": {
      "sound": "notification_158187"
    }
  }
}
```

## Testing

### 1. Check Console Logs
Look for these messages in the app logs:
- ✅ `"Android custom sound: notification_158187 configured in raw resources"`
- ✅ `"iOS custom sound: notification_158187 should be configured in Xcode"`
- ✅ `"Custom sound file: notification_158187 is available in raw resources"`

### 2. Test Notifications
1. Send test notification from Airship dashboard
2. Include the sound configuration in payload
3. Verify custom sound plays instead of default system sound

### 3. Troubleshooting

**Android Issues**:
- Ensure `notification_158187` exists in `android/app/src/main/res/raw/`
- Check notification payload uses `"sound": "charge_start"` (no extension)
- Verify notification permissions are granted

**iOS Issues**:
- Ensure sound file is added to Xcode project
- Check file is included in app bundle
- Verify notification payload uses `"sound": "notification_158187"` (with extension)
- Confirm iOS notification permissions are granted

## Current Status

- ✅ **Android**: Fully configured and ready
- ⚠️ **iOS**: Requires Xcode configuration (sound file needs to be added to bundle)
- ✅ **Airship**: Client-side configuration complete
- 📋 **Server**: Requires payload configuration in Airship dashboard

## Next Steps

1. **For iOS**: Follow the iOS setup steps above
2. **For Testing**: Configure notification payloads in Airship dashboard
3. **For Production**: Ensure all notification campaigns include sound configuration
