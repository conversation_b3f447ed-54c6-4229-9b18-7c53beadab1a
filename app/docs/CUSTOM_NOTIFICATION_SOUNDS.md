# Custom Notification Sounds Configuration

This guide explains how to configure custom notification sounds for both Android and iOS using Airship push notifications.

## Overview

The app uses `charge_start.mp3` as the custom notification sound. This sound is configured differently for Android and iOS platforms.

## Android Configuration

### ✅ Files Already Configured
- **Sound file**: `android/app/src/main/res/raw/charge_start.mp3` ✅
- **Notification icon**: `android/app/src/main/res/drawable/ic_notification.xml` ✅
- **Airship config**: Default channel ID set to `bp_pulse_notifications` ✅

### 🔧 Airship Dashboard Configuration

To use the custom sound, configure the notification payload in Airship dashboard:

```json
{
  "notification": {
    "alert": "Your notification message",
    "android": {
      "sound": "charge_start"
    }
  }
}
```

**Note**: For Android, use the filename WITHOUT the `.mp3` extension.

## iOS Configuration

### ⚠️ Files Need to be Added
- **Sound file**: Needs to be added to iOS bundle via Xcode
- **Xcode configuration**: Sound file must be added to project targets

### 📱 iOS Setup Steps

1. **Add sound file to Xcode**:
   ```bash
   # Copy the sound file to iOS resources
   cp android/app/src/main/res/raw/charge_start.mp3 ios/charge_start.mp3
   ```

2. **Open Xcode**:
   ```bash
   cd ios
   open bppulse.xcworkspace
   ```

3. **Add file to project**:
   - Right-click on project in Xcode
   - Select "Add Files to 'bppulse'"
   - Choose `charge_start.mp3`
   - Ensure it's added to both app targets

4. **Verify bundle inclusion**:
   - Select the sound file in Xcode
   - Check "Target Membership" in right panel
   - Ensure both targets are checked

### 🔧 Airship Dashboard Configuration for iOS

```json
{
  "notification": {
    "alert": "Your notification message",
    "ios": {
      "sound": "charge_start.mp3"
    }
  }
}
```

**Note**: For iOS, use the filename WITH the `.mp3` extension.

## Combined Configuration

For notifications that work on both platforms:

```json
{
  "notification": {
    "alert": "Your notification message",
    "android": {
      "sound": "charge_start"
    },
    "ios": {
      "sound": "charge_start.mp3"
    }
  }
}
```

## Testing

### 1. Check Console Logs
Look for these messages in the app logs:
- ✅ `"Android custom sound: charge_start.mp3 configured in raw resources"`
- ✅ `"iOS custom sound: charge_start.mp3 should be configured in Xcode"`
- ✅ `"Custom sound file: charge_start.mp3 is available in raw resources"`

### 2. Test Notifications
1. Send test notification from Airship dashboard
2. Include the sound configuration in payload
3. Verify custom sound plays instead of default system sound

### 3. Troubleshooting

**Android Issues**:
- Ensure `charge_start.mp3` exists in `android/app/src/main/res/raw/`
- Check notification payload uses `"sound": "charge_start"` (no extension)
- Verify notification permissions are granted

**iOS Issues**:
- Ensure sound file is added to Xcode project
- Check file is included in app bundle
- Verify notification payload uses `"sound": "charge_start.mp3"` (with extension)
- Confirm iOS notification permissions are granted

## Current Status

- ✅ **Android**: Fully configured and ready
- ⚠️ **iOS**: Requires Xcode configuration (sound file needs to be added to bundle)
- ✅ **Airship**: Client-side configuration complete
- 📋 **Server**: Requires payload configuration in Airship dashboard

## Next Steps

1. **For iOS**: Follow the iOS setup steps above
2. **For Testing**: Configure notification payloads in Airship dashboard
3. **For Production**: Ensure all notification campaigns include sound configuration
